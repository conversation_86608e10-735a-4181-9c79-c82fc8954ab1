<!DOCTYPE html>
<html>
<head>
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>
  <%= javascript_include_tag 'vue' %>
  <%= stylesheet_link_tag 'font', media: 'all' %>
  <%= javascript_include_tag 'moment' %>
  <%= javascript_include_tag 'tailwind' %>
  <%= javascript_include_tag 'jsbarcode' %>
</head>
<body>
<div id="app", style="font-size: 10px">
  <div v-if="reportData.length > 0" class="px-5 print-container">
        <div class="rounded-tr rounded-tl px-5 py-5">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <img
                src="/images/logo.png"
                alt="app-logo"
                class="w-24 h-24 object-cover"
              />
              <h3 class="text-2xl font-semibold" style="font-size: 16px;">PATIENT REPORT</h3>
            </div>
            <div>
              <svg id="barcode">
              </svg>
            </div>
            <div class="bg-gray-50 px-4 py-1 rounded border border-dotted">
              <address class="font-normal">
                <span
                  class="flex items-center not-italic text-xl font-semibold border-b mb-2 border-dotted"
                  style="font-size: 14px; line-height: 2rem;"
                >
                  {{ facility.name}}
                </span>
                <span class="flex items-center not-italic text-gray-600">
                  {{ facility.address }}
                </span>
                <span class="flex items-center not-italic text-gray-600">
                  {{ facility.phone }}
                </span>
              </address>
            </div>
          </div>

          <div class="flex items-center justify-between mt-5">
            <div>
              <span class="font-medium">Report Date:</span>
              {{ moment(new Date()).format(dateFormat) }}
            </div>
            <div>
              <span class="font-medium">No. Printed:</span>
              {{ getPrintCount(reportData)}}
            </div>
            <div>
              <span class="font-medium">Date Sample Collected:</span>
              {{
                reportData.length > 0 &&
                moment(reportData[0].sample_collection_time).format(dateFormat)
              }}
            </div>
          </div>

          <table class="w-full mt-2">
            <tbody>
              <tr>
                <td class="border px-1 py-1 font-bold">Patient Name</td>
                <td class="border px-1 py-1">
                  {{
                    `${person.first_name} ${
                      person.middle_name ? person.middle_name : ""
                    } ${person.last_name}`
                  }}
                </td>
                <td class="border-t border-l px-1 py-1 font-bold">Sex: </td>
                <td class="border-t px-1 py-1">
                  {{ person.sex === "F" ? "Female" : "Male" }}
                </td>
                <td class="border-t border-l px-1 py-1 font-bold">Age: </td>
                <td class="border-t border-r px-1 py-1">
                  {{ getPatientAge(person.date_of_birth) }}
                </td>
              </tr>
              <tr>
                <td class="border px-1 py-1 font-bold">Patient ID</td>
                <td class="border px-1 py-1">{{ person.id }}</td>
                <td class="border px-1 py-1 font-bold" colspan="2">Address</td>
                <td class="border px-1 py-1" colspan="2">
                  {{ getPhysicalAddress }}
                </td>
              </tr>
            </tbody>
          </table>


        <div v-for="(report, index) in reportData" :key="index">
          <div
            class="bg-gray-100 text-gray-600 border-b flex items-center justify-between px-2 py-2 font-medium"
          >
            <div class="flex items-center">
              Accession No: {{ report.accession_number }}
            </div>
            <div>
              Requested By: {{ report.requested_by }} ({{
                report.requesting_ward
              }})
            </div>
          </div>

          <table class="border-collapse border-slate-500 w-full">
            <tbody>
              <tr>
                <td class="border px-1 py-1 font-bold">
                  Specimen Type
                </td>
                <td class="border px-1 py-1">
                  {{ use_full_name ? report.specimen : report.specimen_preferred_name || report.specimen }}
                </td>
                <td class="border px-1 py-1 font-bold">
                  Date Registered
                </td>
                <td class="border px-1 py-1">
                  {{ moment(report.created_date).format(dateFormat) }}
                </td>
              </tr>
              <tr>
                <td class="border px-1 py-1 font-bold">
                  Test Type(s)
                </td>
                <td class="border px-1 py-1">
                  {{ getTestTypes(report.test_types) }}
                </td>
                <td class="border px-1 py-1 font-bold">
                  Lab Sections
                </td>
                <td class="border px-1 py-1">
                  {{ getDepartments(report.test_types) }}
                </td>
              </tr>
              <tr>
                <td class="border px-1 py-1 font-bold">
                  Specimen Status
                </td>
                <td class="border px-1 py-1">
                  {{ getOrderStatus(report.order_status) }}
                </td>
                <td class="border px-1 py-1 font-bold">
                  Received By
                </td>
                <td class="border px-1 py-1">
                  {{ getOrderStatusInitiatorName(report.order_status_trail) }}
                </td>
              </tr>
            </tbody>
          </table>

          <table class="border-collapse w-full">
            <thead>
              <tr class="bg-gray-100 text-gray-600">
                <th class="px-1 py-1 text-lg font-semibold">Results</th>
                <th :colspan="3" class="px-1 py-1 text-lg font-semibold">
                  Tests Authorized({{ report.tests_verified }})
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th class="border px-1 py-1 font-bold">Test Types</th>
                <th class="border px-1 py-1 font-bold">Results</th>
                <th class="border px-1 py-1 font-bold">Audit details</th>
              </tr>
              <template
                v-for="test in showPendingTests
                  ? report.tests
                  : getAuthorizedTests(report.tests)"
                :key="test.id"
              >
                <tr>
                  <td class="border px-5 py-1 text-left">
                    {{ use_full_name ? test.test_type_name : test.test_type_preferred_name || test.test_type_name }}
                  </td>
                  <td class="p-0 border-b" style="padding: 0 !important">
                    <table
                      class="w-full border-collapse"
                      style="
                        border-collapse: collapse !important;
                        margin: 0 !important;
                        padding: 0 !important;
                      "
                    >
                      <thead>
                        <tr class="border-b border-t bg-gray-100">
                          <th class="border-r border-b px-1 py-1 font-bold">
                            Measure
                          </th>
                          <th class="border-r border-b px-1 py-1 font-bold">
                            Result
                          </th>
                          <th class="border-r border-b px-1 py-1 font-bold">
                            Units
                          </th>
                          <th class="px-1 py-1 border-b font-bold">Range</th>
                        </tr>
                      </thead>
                      <tbody class="border-b">
                        <template
                          v-for="(indicator, t) in test.indicators"
                          :key="t"
                        >
                          <tr class="border-b">
                            <td class="border-r px-1 py-1">
                              {{ use_full_name ? indicator.name : indicator.preferred_name || indicator.name }}
                            </td>
                            <td class="px-1 py-1 flex items-center border-r">
                              <p
                                v-html="
                                  showResult(
                                    indicator.result?.value ?? '',
                                    test?.suscept_test_result
                                  )
                                "
                              ></p>
                              <span v-if="!indicator.result?.value"
                                >Not done</span
                              >
                            </td>
                            <td class="px-2">{{ indicator.unit }}</td>
                            <td
                              class="border-t border-l px-1 py-1 font-bold"
                              v-if="
                                shouldDisplayRange(
                                  test.indicators,
                                  person,
                                  indicator
                                ) !== undefined &&
                                shouldDisplayRange(
                                  test.indicators,
                                  person,
                                  indicator
                                ) !== null
                              "
                            >
                              ({{
                                shouldDisplayRange(
                                  test.indicators,
                                  person,
                                  indicator
                                ).lower_range
                              }}
                              -
                              {{
                                shouldDisplayRange(
                                  test.indicators,
                                  person,
                                  indicator
                                ).upper_range
                              }})
                            </td>
                            <td class="border px-1 py-1" v-else></td>
                          </tr>
                        </template>
                      </tbody>
                    </table>
                  </td>
                  <td class="border px-1 py-1">
                    <div class="test-status">
                      <p class="font-bold">Test Status</p>
                      <p class="px-1">{{ getTestStatusName(test.status) }}</p>
                      <p class="px-1">
                        By:
                        {{
                          getTestStatusInitiatorName(
                            test.status_trail,
                            test.status
                          )["name"]
                        }}
                      </p>
                      <p class="px-1">
                        On:
                        {{
                          moment(
                            getTestStatusInitiatorName(
                              test.status_trail,
                              test.status
                            )["created_at"]
                          ).format(dateFormat)
                        }}
                      </p>
                    </div>
                    <div
                      v-if="
                        getTestStatusInitiatorName(
                          test.status_trail,
                          'completed'
                        )['name']
                      "
                    >
                      <p class="font-bold">Performed By</p>
                      <p class="px-1">
                        {{
                          getTestStatusInitiatorName(
                            test.status_trail,
                            "completed"
                          )["name"]
                        }}
                      </p>
                      <p class="px-1 mb-2">
                        On:
                        {{
                          moment(
                            getTestStatusInitiatorName(
                              test.status_trail,
                              "completed"
                            )["created_at"]
                          ).format(dateFormat)
                        }}
                      </p>
                      <div v-if="getMachineName(test.indicators)" class="mb-2">
                        <h3 class="font-bold">Laboratory Machine Used</h3>
                        <p>{{ getMachineName(test.indicators) }}</p>
                      </div>
                    </div>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
          <div class="m-2" v-show="checkSusceptibilityResults(report)">
            <h3 class="text-lg font-semibold uppercase mb-3 mt-3">
              Suscpeptibility Test Results
            </h3>
            <div>
              <div
                class="grid grid-cols-2 gap-4"
                v-for="test in report.tests"
                :key="test.id"
              >
                <div
                  v-for="(result, index) in test.suscept_test_result"
                  :key="index"
                  v-if="test.test_type_name.toLowerCase().includes('culture')"
                >
                  <div
                    class="border rounded-tr rounded-tl px-1 py-1 font-medium text-lg"
                  >
                    {{ result.name }}
                  </div>
                  <table class="w-full">
                    <thead>
                      <tr
                        class="border-b px-1 py-1 text-left border-r border-l"
                      >
                        <th class="px-4 py-1 border-r">Drug</th>
                        <th class="px-4 py-1">Intrepretation</th>
                      </tr>
                    </thead>
                    <tbody classs="border-l border-r">
                      <tr
                        v-for="drug in getFilteredDrugs(result.drugs)"
                        :key="drug.drug_id"
                      >
                        <th
                          class="text-left border-l border-r px-4 py-1 border-b font-normal"
                        >
                          {{ drug.name }}
                        </th>
                        <th
                          class="text-left border-l border-r px-4 py-1 border-b font-normal"
                        >
                          {{ drug.interpretation }}
                        </th>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
            <div>
            <div class="bg-gray-100 px-4 py-2 border-b">
              <h3 class="text-lg font-semibold">Test Remarks</h3>
            </div>
            <template v-if="hasAnyRemarks(report.tests)">
              <div v-for="test in showPendingTests ? report.tests : getAuthorizedTests(report.tests)" :key="test.id">
                <div v-if="getRemarks(test) !== 'N/A'" class="mb-3 px-4 border-b border-l border-r pb-2 py-2">
                  <p class="font-medium">{{use_full_name ? test.test_type_name : test.test_type_preferred_name || test.test_type_name }}:</p>
                  <p class="ml-4" v-html="getRemarks(test)"></p>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="px-4 py-2 border-b">
                <p class="text-gray-600">No remarks available for these tests.</p>
              </div>
            </template>
          </div>
        </div>
        </div>
      </div>
</div>

<script>
 const { createApp, ref } = Vue

const app = createApp({
  data() {
    return {
      dateRange: [],
      dateFormat: "DD/MMM/YYYY HH:mm",
      location: {},
      reportData: {},
      moment: moment,
      person_identifiers: [],
      physicalAddress: "",
      person: {},
      facility: {},
      use_full_name: true,
      hasSusceptivibility: false,
      authorizedTestCount: 0,
      showPendingTests: false,
      loading: false
    };
  },
  methods: {
    getOrderStatusInitiatorName(data) {
      const specimenStatus = ["specimen-accepted", "specimen-rejected"];
      const result = data.find((item) =>
        specimenStatus.includes(item.status.name)
      );
      return result
        ? `${result.initiator.first_name} ${result.initiator.last_name}`
        : "";
    },
    getOrderStatus(status) {
      return status !== null ? this.capitalizeStr(status.split("-").join(" ")) : "";
    },
    getTestStatusInitiatorName(data, status) {
      const result = data.find((item) => status === item.status.name);
      return result
        ? {
            name: `${result.initiator.first_name} ${result.initiator.last_name}`,
            created_at: result.created_date,
          }
        : {
            name: "",
            created_at: "",
          };
    },
    getTestTypes(data) {
      if(this.use_full_name) {
        return data.map((item) => item.name).join(", ");
      }
      return data.map((item) => item.preferred_name || item.name).join(", ");
    },
    getDepartments(data) {
      const uniqueDepartments = [
        ...new Set(data.map((item) => item.department)),
      ];
      return uniqueDepartments.join(", ");
    },
    getAuthorizedTestCount(data) {
      return data.reduce((count, order) => count + order.tests_verified, 0);
    },
    showIndicatorRanges(data) {
      return data.some(
        (indicator) => indicator.test_indicator_type === "numeric"
      );
    },
    getTestStatusName(status) {
      if (status == "verified") {
        status = "Authorized";
      } else {
        status = status
          .split("-")
          .map((word) => this.capitalizeStr(word))
          .join(" ");
      }
      return status;
    },
    capitalizeStr(str) {
      return str.charAt(0).toUpperCase() + str.slice(1);
    },
    updateReport(value) {
      if (value) {
        this.init();
      }
    },
    checkSusceptibilityResults(report) {
      return report.tests.some(
        (test) => test.suscept_test_result.length !== 0 && this.getAuthorizedTests(report.tests).length > 0
      );
    },
    getFilteredDrugs(drugs) {
      return drugs.filter((drug) => drug.zone !== null);
    },
    showResult(result, susceptibilityResults) {
      return susceptibilityResults.length > 0 && result.toLowerCase() === 'growth'
        ? `Growth of ${[...new Set(susceptibilityResults)]
            .map((result) => result.name)
            .join(", ")}`
        : result;
    },
    getPrintCount(reportData) {
      if (reportData.length > 1 ){
        let report_count = 1;
        reportData.forEach((report) => {
          report_count += (report.print_count == 0 ? 1 : report.print_count);
        });
        return (report_count - reportData.length) + 1
      } else {
        return reportData.reduce(
          (count, report) => count + report.print_count + 1,
          0
        )
      };
    },
    calculateAge(date) {
      return moment().diff(date, "years");
    },
    getPatientAge(date) {
      const now = moment();
      const start = moment(date);
      const years = now.diff(start, "years");
      start.add(years, "years");
      const months = now.diff(start, "months");
      start.add(months, "months");
      const weeks = now.diff(start, "weeks");
      start.add(weeks, "weeks");
      const days = now.diff(start, "days");
      start.add(days, "days");
      const hrs = now.diff(start, "hours");
      return `${years} yrs ${months} months ${weeks} wks ${days} days ${hrs} hrs`;
    },
    getRequiredRange(patient, ranges) {
      const patientSex = patient.sex == "M" ? "Male" : "Female";
      const age = Math.ceil(
        Number(
          this.calculateAge(patient.date_of_birth) == 0
            ? 1
            : this.calculateAge(patient.date_of_birth)
        )
      );
      return ranges.filter(
        (range) =>
          (range.sex === patientSex &&
            age > Number(range.min_age) &&
            age < Number(range.max_age)) ||
          (range.sex === "Both" &&
            age > Number(range.min_age) &&
            age < Number(range.max_age))
      )[0];
    },
    getRemarks(test) {
      const rejectedReason = test?.status_trail.find((trail) => trail.status.name.toLowerCase() === "test-rejected");
      let reasonDescription = "N/A";
      if (rejectedReason) {
        reasonDescription = `<b>Rejected Reason:</b> ${rejectedReason?.status_reason?.description}`;
      }
      const remarks = test?.result_remarks?.value;
      if (remarks && reasonDescription !== "N/A") {
        return `${remarks}<br>${reasonDescription}`;
      }
      return remarks || reasonDescription;
    },
    shouldDisplayRange(
      indicators,
      patient,
      indicator
    ) {
      let result = null
      if (this.showIndicatorRanges(indicators)) {
        result = this.getRequiredRange(patient, indicator.indicator_ranges);
      }
      return result;
    },
    getMachineName(indicators) {
      return indicators[0].result.machine_name;
    },
    getCompletedTests(tests){
      const validStatuses = new Set(["completed", "verified", "test-rejected", "rejected"]);
      return tests.filter((item) =>
        validStatuses.has(item.status.toLowerCase())
      );
    },
    getAuthorizedTests(tests){
      const validStatuses = new Set(["completed", "verified", "test-rejected", "rejected"]);
      return tests.filter((item) =>
        validStatuses.has(item.status.toLowerCase())
      );
    },
    hasAnyRemarks(tests) {
      return tests.some(test => {
        const remarks = this.getRemarks(test);
        return remarks && remarks !== "N/A" && remarks !== '';
      });
    }
  },
  mounted() {
    let data = <%= raw @test_service.to_json %>
    this.facility = data.facility
    this.use_full_name = data.use_full_name
    data = data.data
    this.person = data.client.person;
    this.person_identifiers = data.client.client_identifiers;
    let formattedData = data.orders.map((order) => {
      let count = 0;
      if (order.tests !== undefined) {
        count += order.tests.filter(
          (test) =>
            test.status.toLowerCase() === "verified"
        ).length;
      }
      return {
        ...order,
        tests_verified: count,
      };
    });
    this.reportData = formattedData;
    this.$nextTick(() => {
      JsBarcode("#barcode", this.reportData[0].accession_number, {
        format: "CODE128",
        height: 50,
        displayValue: false
      });
    });

  }
}).mount('#app')
</script>
</body>
</html>
