# frozen_string_literal: true

require 'test_helper'

class Nlims::RemoteServiceV2Test < ActiveSupport::TestCase
  def setup
    @service = Nlims::RemoteService.new(
      base_url: 'http://localhost',
      username: 'test',
      password: 'test',
      token: 'test_token'
    )
    
    # Create test data
    @department = Department.create!(name: 'Test Department')
    @lab_location = LabLocation.create!(name: 'Test Lab')
    @specimen = Specimen.create!(
      name: 'Venous Whole Blood',
      nlims_code: 'NLIMS_SP_0003_MWI',
      preferred_name: 'Blood'
    )
    @test_type = TestType.create!(
      name: 'Full Blood Count',
      nlims_code: 'NLIMS_TT_0035_MWI',
      preferred_name: 'FBC',
      department: @department
    )
    @test_indicator = TestIndicator.create!(
      name: 'White Blood Cell',
      nlims_code: 'NLIMS_TI_0605_MWI',
      preferred_name: 'WBC'
    )
    @status = Status.create!(name: 'verified')
  end

  def test_find_specimen_by_nlims_code
    sample_type = {
      'nlims_code' => 'NLIMS_SP_0003_MWI',
      'name' => 'Venous Whole Blood'
    }
    
    specimen = @service.find_specimen_by_nlims_code_or_name(sample_type)
    assert_equal @specimen.id, specimen.id
  end

  def test_find_test_type_by_nlims_code
    test_type_data = {
      'nlims_code' => 'NLIMS_TT_0035_MWI',
      'name' => 'Full Blood Count'
    }
    
    test_type = @service.find_test_type_by_nlims_code_or_name(test_type_data)
    assert_equal @test_type.id, test_type.id
  end

  def test_find_test_indicator_by_nlims_code
    measure_data = {
      'nlims_code' => 'NLIMS_TI_0605_MWI',
      'name' => 'White Blood Cell'
    }
    
    indicator = @service.find_test_indicator_by_nlims_code_or_name(measure_data)
    assert_equal @test_indicator.id, indicator.id
  end

  def test_extract_drawn_by_name
    drawn_by_data = {
      'name' => 'chacho namaheya',
      'id' => '678250'
    }
    
    name = @service.extract_drawn_by_name(drawn_by_data)
    assert_equal 'chacho namaheya', name
  end

  def test_extract_drawn_by_name_with_nil
    name = @service.extract_drawn_by_name(nil)
    assert_equal '', name
  end

  private

  def sample_nlims_payload
    {
      'order' => {
        'uuid' => 'b97a7258-10bb-47f4-accf-b49cad2328f8',
        'tracking_number' => 'XKCH2500874465',
        'sample_type' => {
          'nlims_code' => 'NLIMS_SP_0003_MWI',
          'name' => 'Venous Whole Blood'
        },
        'priority' => 'Routine',
        'requested_by' => 'Dr',
        'date_created' => '2025-02-22T18:45:04.000+02:00',
        'drawn_by' => {
          'name' => 'chacho namaheya',
          'id' => '678250'
        },
        'target_lab' => 'Kamuzu Central Hospital',
        'sending_facility' => 'Kamuzu Central Hospital',
        'order_location' => 'MSS'
      },
      'patient' => {
        'national_patient_id' => 'Unknown',
        'first_name' => 'Eflida',
        'last_name' => 'Madalitso',
        'gender' => 'M',
        'date_of_birth' => '1992-02-22'
      },
      'tests' => [
        {
          'test_type' => {
            'nlims_code' => 'NLIMS_TT_0035_MWI',
            'name' => 'Full Blood Count'
          },
          'test_status' => 'verified',
          'status_trail' => [
            {
              'status' => 'pending',
              'timestamp' => '2025-02-22T18:45:04.000+02:00'
            },
            {
              'status' => 'verified',
              'timestamp' => '2025-02-22T19:16:28.000+02:00'
            }
          ],
          'test_results' => [
            {
              'measure' => {
                'nlims_code' => 'NLIMS_TI_0605_MWI',
                'name' => 'White Blood Cell'
              },
              'result' => {
                'value' => '4.9',
                'unit' => '10^3/uL',
                'result_date' => '2025-02-22T19:16:21.000+02:00'
              }
            }
          ]
        }
      ],
      'lab_location' => 1
    }
  end
end
