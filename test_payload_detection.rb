#!/usr/bin/env ruby

# Test script to verify payload detection logic

require 'json'

# Simulate ActionController::Parameters behavior
class MockParams < Hash
  def key?(key)
    super(key.to_s) || super(key.to_sym)
  end
end

def new_payload_format?(params)
  # New format has 'order', 'patient', and 'tests' keys at the top level
  params.key?('order') && params.key?('patient') && params.key?('tests')
end

# Test with new payload format
new_payload = MockParams.new({
  'order' => {
    'uuid' => 'b97a7258-10bb-47f4-accf-b49cad2328f8',
    'tracking_number' => 'XKCH2500874465',
    'sample_type' => {
      'nlims_code' => 'NLIMS_SP_0003_MWI',
      'name' => 'Venous Whole Blood'
    }
  },
  'patient' => {
    'national_patient_id' => 'Unknown',
    'first_name' => 'Eflida',
    'last_name' => 'Madalitso'
  },
  'tests' => [
    {
      'test_type' => {
        'nlims_code' => 'NLIMS_TT_0035_MWI',
        'name' => 'Full Blood Count'
      }
    }
  ],
  'lab_location' => 1
})

# Test with old payload format
old_payload = MockParams.new({
  'tracking_number' => 'XKCH2500874465',
  'specimen' => 'Venous Whole Blood',
  'tests' => [
    {
      'test_type' => 'Full Blood Count',
      'test_status' => 'verified'
    }
  ],
  'priority' => 'Routine'
})

puts "New payload format detected: #{new_payload_format?(new_payload)}"
puts "Old payload format detected: #{new_payload_format?(old_payload)}"

# Test check_test_type method logic
def check_test_type_logic(test_type)
  # Handle both string and object formats
  test_type_name = if test_type.is_a?(String)
                     test_type
                   elsif test_type.respond_to?(:[])
                     test_type[:name] || test_type['name']
                   else
                     test_type.to_s
                   end
  
  puts "Input: #{test_type.inspect}"
  puts "Extracted name: #{test_type_name}"
  puts "---"
end

puts "\nTesting check_test_type logic:"
check_test_type_logic("Full Blood Count")
check_test_type_logic({'name' => 'Full Blood Count', 'nlims_code' => 'NLIMS_TT_0035_MWI'})
check_test_type_logic({name: 'Full Blood Count', nlims_code: 'NLIMS_TT_0035_MWI'})
