{"order": {"uuid": "b97a7258-10bb-47f4-accf-b49cad2328f8", "tracking_number": "XKCH2500874465", "sample_type": {"id": 3, "name": "Venous Whole Blood", "description": "<p>Deoxygenated blood which travels from the peripheral blood vessels, through the venous system into the right atrium of the heart.</p>", "created_at": "2021-04-14T14:13:47.000+02:00", "updated_at": "2025-04-19T22:14:58.000+02:00", "moh_code": null, "nlims_code": "NLIMS_SP_0003_MWI", "loinc_code": null, "preferred_name": "Blood", "scientific_name": "Venous Whole Blood", "iblis_mapping_name": "Blood"}, "sample_status": {"id": 2, "name": "specimen_accepted", "created_at": "2021-04-21T16:13:10.000+02:00", "updated_at": "2021-04-21T16:13:10.000+02:00", "short_name": null, "moh_code": null, "nlims_code": null, "loinc_code": null, "preferred_name": null, "scientific_name": null, "description": null}, "order_location": "MSS", "date_created": "2025-02-22T18:45:04.000+02:00", "priority": "Routine", "reason_for_test": "Routine", "drawn_by": {"id": "678250", "name": "chacho  namah<PERSON>a", "phone_number": ""}, "target_lab": "Kamuzu Central Hospital", "sending_facility": "Kamuzu Central Hospital", "district": "Lilongwe", "site_code_number": "1520", "requested_by": "Dr", "art_start_date": null, "arv_number": "N/A", "art_regimen": "N/A", "clinical_history": null, "lab_location": "Main Lab", "source_system": "IBLIS", "status_trail": []}, "patient": {"id": 5, "national_patient_id": "Unknown", "first_name": "<PERSON><PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "gender": "M", "date_of_birth": "1992-02-22", "address": null, "email": null, "phone_number": null}, "tests": [{"tracking_number": "XKCH2500874465", "arv_number": "N/A", "uuid": "b97a7258-10bb-47f4-accf-b49cad2328f8", "test_status": "verified", "time_updated": "2025-02-22T19:16:28.000+02:00", "test_type": {"id": 35, "test_category_id": 2, "name": "Full Blood Count", "short_name": "FBC", "targetTAT": "2 Hours", "description": "<p>To evaluate overall health and wide of disorders including anaemia, infections, leukaemia, blood cell components abnormalities, immune disorders.</p>", "prevalence_threshold": "", "created_at": "2021-04-14T14:13:47.000+02:00", "updated_at": "2025-04-29T11:50:39.000+02:00", "moh_code": null, "nlims_code": "NLIMS_TT_0035_MWI", "loinc_code": null, "preferred_name": "FBC", "scientific_name": "Full Blood Count", "can_be_done_on_sex": "Both", "assay_format": "Automated Haematology analyser", "hr_cadre_required": "Lab Technician", "iblis_mapping_name": "FBC"}, "status_trail": [{"status_id": 2, "status": "pending", "timestamp": "2025-02-22T18:45:04.000+02:00", "updated_by": {"first_name": "chacho", "last_name": "na<PERSON><PERSON>a", "id": "121", "phone_number": ""}}, {"status_id": 3, "status": "started", "timestamp": "2025-02-22T18:57:54.000+02:00", "updated_by": {"first_name": "chacho", "last_name": "na<PERSON><PERSON>a", "id": "121", "phone_number": ""}}, {"status_id": 4, "status": "completed", "timestamp": "2025-02-22T19:16:21.000+02:00", "updated_by": {"first_name": "chacho", "last_name": "na<PERSON><PERSON>a", "id": "121", "phone_number": ""}}, {"status_id": 5, "status": "verified", "timestamp": "2025-02-22T19:16:28.000+02:00", "updated_by": {"first_name": "chacho", "last_name": "na<PERSON><PERSON>a", "id": "121", "phone_number": ""}}], "test_results": [{"measure": {"name": "White Blood Cell", "nlims_code": "NLIMS_TI_0605_MWI", "moh_code": null, "loinc_code": null, "preferred_name": "WBC", "scientific_name": "White Blood Cell", "short_name": "WBC", "measure_type": "Numeric"}, "result": {"value": "4.9", "unit": "10^3/uL", "result_date": "2025-02-22T19:16:21.000+02:00", "platform": "sysmex-xn530", "platformserial": ""}}, {"measure": {"name": "Red Blood Cell", "nlims_code": "NLIMS_TI_0604_MWI", "moh_code": null, "loinc_code": null, "preferred_name": "RBC", "scientific_name": "Red Blood Cell", "short_name": "RBC", "measure_type": "Numeric"}, "result": {"value": "-1.34", "unit": "10^6/uL", "result_date": "2025-02-22T19:16:21.000+02:00", "platform": "sysmex-xn530", "platformserial": ""}}]}, {"tracking_number": "XKCH2500874465", "arv_number": "N/A", "uuid": "b97a7258-10bb-47f4-accf-b49cad2328f8", "test_status": "completed", "time_updated": "2025-02-22T20:34:26.000+02:00", "test_type": {"id": 29, "test_category_id": 3, "name": "ABO And RhD Blood Grouping", "short_name": "ABO", "targetTAT": "30 Minutes", "description": "<p>To determine the ABO and RhD blood typing for all antenatal women</p>", "prevalence_threshold": "", "created_at": "2021-04-14T14:13:47.000+02:00", "updated_at": "2025-10-30T11:17:22.000+02:00", "moh_code": null, "nlims_code": "NLIMS_TT_0029_MWI", "loinc_code": null, "preferred_name": "ABO Blood Grouping", "scientific_name": "ABO And RhD Blood Grouping", "can_be_done_on_sex": "Both", "assay_format": "tile method", "hr_cadre_required": "clinicians, nurses and any other trained staff", "iblis_mapping_name": "ABO Blood Grouping"}, "status_trail": [{"status_id": 2, "status": "pending", "timestamp": "2025-02-22T20:32:36.000+02:00", "updated_by": {"first_name": "chacho", "last_name": "na<PERSON><PERSON>a", "id": "121", "phone_number": ""}}, {"status_id": 3, "status": "started", "timestamp": "2025-02-22T20:34:18.000+02:00", "updated_by": {"first_name": "chacho", "last_name": "na<PERSON><PERSON>a", "id": "121", "phone_number": ""}}, {"status_id": 4, "status": "completed", "timestamp": "2025-02-22T20:34:26.000+02:00", "updated_by": {"first_name": "chacho", "last_name": "na<PERSON><PERSON>a", "id": "121", "phone_number": ""}}], "test_results": []}, {"tracking_number": "XKCH2500874465", "arv_number": "N/A", "uuid": "b97a7258-10bb-47f4-accf-b49cad2328f8", "test_status": "verified", "time_updated": "2025-02-22T20:35:11.000+02:00", "test_type": {"id": 30, "test_category_id": 3, "name": "Cross-match", "short_name": "Xmatch", "targetTAT": "1 Hours", "description": "<p>To determine blood compatibility for blood transfusions</p>", "prevalence_threshold": "", "created_at": "2021-04-14T14:13:47.000+02:00", "updated_at": "2025-08-07T10:05:46.000+02:00", "moh_code": null, "nlims_code": "NLIMS_TT_0030_MWI", "loinc_code": null, "preferred_name": "Cross-match", "scientific_name": "Cross-match", "can_be_done_on_sex": "Both", "assay_format": " Agglutination test", "hr_cadre_required": "Lab technician", "iblis_mapping_name": "Cross-match"}, "status_trail": [{"status_id": 2, "status": "pending", "timestamp": "2025-02-22T20:33:05.000+02:00", "updated_by": {"first_name": "chacho", "last_name": "na<PERSON><PERSON>a", "id": "121", "phone_number": ""}}, {"status_id": 3, "status": "started", "timestamp": "2025-02-22T20:34:32.000+02:00", "updated_by": {"first_name": "chacho", "last_name": "na<PERSON><PERSON>a", "id": "121", "phone_number": ""}}, {"status_id": 4, "status": "completed", "timestamp": "2025-02-22T20:35:06.000+02:00", "updated_by": {"first_name": "chacho", "last_name": "na<PERSON><PERSON>a", "id": "121", "phone_number": ""}}, {"status_id": 5, "status": "verified", "timestamp": "2025-02-22T20:35:11.000+02:00", "updated_by": {"first_name": "chacho", "last_name": "na<PERSON><PERSON>a", "id": "121", "phone_number": ""}}, {"status_id": 6, "status": "voided", "timestamp": "2025-03-06T14:38:25.000+02:00", "updated_by": {"first_name": "<PERSON><PERSON>", "last_name": "Kachigamba", "id": "131", "phone_number": ""}}], "test_results": [{"measure": {"name": "Pack No.", "nlims_code": "NLIMS_TI_0126_MWI", "moh_code": null, "loinc_code": null, "preferred_name": "Pack No.", "scientific_name": "Pack No.", "short_name": "Pack No.", "measure_type": "Free Text"}, "result": {"value": "19/03/2025$", "unit": "", "result_date": "2025-02-22T20:35:06.000+02:00", "platform": "", "platformserial": ""}}, {"measure": {"name": "Pack ABO Group", "nlims_code": "NLIMS_TI_0127_MWI", "moh_code": null, "loinc_code": null, "preferred_name": "Pack ABO Group", "scientific_name": "Pack ABO Group", "short_name": "Pack ABO Group", "measure_type": "AutoComplete"}, "result": {"value": "O RhD Positive ", "unit": "", "result_date": "2025-02-22T20:35:06.000+02:00", "platform": "", "platformserial": ""}}, {"measure": {"name": "Product Type", "nlims_code": "NLIMS_TI_0128_MWI", "moh_code": null, "loinc_code": null, "preferred_name": "Product Type", "scientific_name": "Product Type", "short_name": "Product Type", "measure_type": "AutoComplete"}, "result": {"value": "Whole Blood", "unit": "", "result_date": "2025-02-22T20:35:06.000+02:00", "platform": "", "platformserial": ""}}, {"measure": {"name": "Volume", "nlims_code": "NLIMS_TI_0102_MWI", "moh_code": null, "loinc_code": null, "preferred_name": "Volume", "scientific_name": "Volume", "short_name": "Volume", "measure_type": "Numeric"}, "result": {"value": "450", "unit": "mL", "result_date": "2025-02-22T20:35:06.000+02:00", "platform": "", "platformserial": ""}}, {"measure": {"name": "Cross-match Method", "nlims_code": "NLIMS_TI_0131_MWI", "moh_code": null, "loinc_code": null, "preferred_name": "Cross-match Method", "scientific_name": "Cross-match Method", "short_name": "Cross-match Method", "measure_type": "AutoComplete"}, "result": {"value": "Saline", "unit": "", "result_date": "2025-02-22T20:35:06.000+02:00", "platform": "", "platformserial": ""}}]}], "lab_location": 1}